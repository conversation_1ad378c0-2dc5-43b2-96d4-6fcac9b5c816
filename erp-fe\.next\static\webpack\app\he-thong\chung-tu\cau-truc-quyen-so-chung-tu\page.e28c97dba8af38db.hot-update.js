"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInputTableColumns: function() { return /* binding */ getInputTableColumns; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/index.ts\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n\n\n\n\n\n\nconst getInputTableColumns = (param)=>{\n    let { onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return [\n        {\n            field: \"ma_ct\",\n            headerName: \"M\\xe3 chứng từ\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    // onValueChange={(newValue: any) => {\n                    //   onCellValueChange(params.row.uuid, 'ma_ct', newValue);\n                    // }}\n                    searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].CHUNG_TU, \"/\"),\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.chungTuSearchColumns,\n                    dialogTitle: \"Danh mục chứng từ\",\n                    className: \"w-full\",\n                    columnDisplay: \"ma_ct\",\n                    value: params.row.ma_ct || \"\",\n                    onRowSelection: (selectedChungTu)=>{\n                        if (selectedChungTu) {\n                            console.log(\"selectedChungTu\", selectedChungTu);\n                            onCellValueChange(params.row.uuid, \"ma_ct\", selectedChungTu.ma_ct);\n                            onCellValueChange(params.row.uuid, \"ten_ct\", selectedChungTu.ten_ct || \"\");\n                            if (onChungTuSelect) {\n                                onChungTuSelect(params.row.uuid, selectedChungTu);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"ten_ct\",\n            headerName: \"T\\xean chứng từ\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ten_ct\",\n                    type: \"text\",\n                    value: params.row.ten_ct,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"ten_ct\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            field: \"username\",\n            headerName: \"Người sử dụng Nh\\xf3m\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.username || \"\",\n                    onValueChange: (newValue)=>{\n                        onCellValueChange(params.row.uuid, \"username\", newValue);\n                        // Khi người dùng xóa giá trị, cũng xóa related field\n                        if (!newValue) {\n                            onCellValueChange(params.row.uuid, \"first_name\", \"\");\n                        }\n                    },\n                    otherSearchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].NGUOI_SU_DUNG),\n                    searchEndpoint: \"\",\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.userSearchColumns,\n                    dialogTitle: \"Danh mục người sử dụng\",\n                    className: \"w-full\",\n                    displayRelatedField: \"first_name\",\n                    columnDisplay: \"username\",\n                    relatedFieldValue: params.row.first_name || \"\",\n                    onRowSelection: (selectedUser)=>{\n                        if (selectedUser) {\n                            onCellValueChange(params.row.uuid, \"username\", selectedUser.username);\n                            onCellValueChange(params.row.uuid, \"first_name\", selectedUser.first_name);\n                            // Call parent callback for additional processing\n                            if (onUserSelect) {\n                                onUserSelect(params.row.uuid, selectedUser);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"first_name\",\n            headerName: \"T\\xean người sử dụng\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"first_name\",\n                    type: \"text\",\n                    value: params.row.first_name,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"first_name\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\n"));

/***/ })

});