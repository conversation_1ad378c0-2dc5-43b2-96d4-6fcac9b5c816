"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInputTableColumns: function() { return /* binding */ getInputTableColumns; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/index.ts\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n\n\n\n\n\n\nconst getInputTableColumns = (param)=>{\n    let { onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return [\n        {\n            field: \"ma_ct\",\n            headerName: \"M\\xe3 chứng từ\",\n            width: 175,\n            renderCell: (params)=>{\n                var _params_row_ma_ct_data;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: ((_params_row_ma_ct_data = params.row.ma_ct_data) === null || _params_row_ma_ct_data === void 0 ? void 0 : _params_row_ma_ct_data.ma_ct) || \"\",\n                    // onValueChange={(newValue: any) => {\n                    //   onCellValueChange(params.row.uuid, 'ma_ct', newValue);\n                    // }}\n                    searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].CHUNG_TU),\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.chungTuSearchColumns,\n                    dialogTitle: \"Danh mục chứng từ\",\n                    className: \"w-full\",\n                    columnDisplay: \"ma_ct\",\n                    onRowSelection: (selectedChungTu)=>{\n                        if (selectedChungTu) {\n                            console.log(\"selectedChungTu\", params);\n                            onCellValueChange(params.row.uuid, \"ma_ct_data\", selectedChungTu || \"\");\n                            // onCellValueChange(params.row.uuid, 'ten_ct', selectedChungTu.ten_ct || '');\n                            if (onChungTuSelect) {\n                                onChungTuSelect(params.row.uuid, selectedChungTu);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"ten_ct\",\n            headerName: \"T\\xean chứng từ\",\n            width: 200,\n            renderCell: (params)=>{\n                var _params_row_ma_ct_data;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ten_ct\",\n                    type: \"text\",\n                    value: ((_params_row_ma_ct_data = params.row.ma_ct_data) === null || _params_row_ma_ct_data === void 0 ? void 0 : _params_row_ma_ct_data.ten_ct) || \"\",\n                    // onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_ct', newValue)}\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, undefined);\n            }\n        },\n        {\n            field: \"username\",\n            headerName: \"Người sử dụng Nh\\xf3m\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.username || \"\",\n                    onValueChange: (newValue)=>{\n                        onCellValueChange(params.row.uuid, \"username\", newValue);\n                        // Khi người dùng xóa giá trị, cũng xóa related field\n                        if (!newValue) {\n                            onCellValueChange(params.row.uuid, \"first_name\", \"\");\n                        }\n                    },\n                    otherSearchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].NGUOI_SU_DUNG),\n                    searchEndpoint: \"\",\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.userSearchColumns,\n                    dialogTitle: \"Danh mục người sử dụng\",\n                    className: \"w-full\",\n                    displayRelatedField: \"first_name\",\n                    columnDisplay: \"username\",\n                    relatedFieldValue: params.row.first_name || \"\",\n                    onRowSelection: (selectedUser)=>{\n                        if (selectedUser) {\n                            onCellValueChange(params.row.uuid, \"username\", selectedUser.username);\n                            onCellValueChange(params.row.uuid, \"first_name\", selectedUser.first_name);\n                            // Call parent callback for additional processing\n                            if (onUserSelect) {\n                                onUserSelect(params.row.uuid, selectedUser);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"first_name\",\n            headerName: \"T\\xean người sử dụng\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"first_name\",\n                    type: \"text\",\n                    value: params.row.first_name,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"first_name\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\n"));

/***/ })

});