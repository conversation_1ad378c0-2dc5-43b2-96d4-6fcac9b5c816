"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInputTableColumns: function() { return /* binding */ getInputTableColumns; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/index.ts\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n\n\n\n\n\n\nconst getInputTableColumns = (param)=>{\n    let { onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return [\n        {\n            field: \"ma_ct\",\n            headerName: \"M\\xe3 chứng từ\",\n            width: 175,\n            renderCell: (params)=>{\n                var _params_row_ma_ct_data;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: ((_params_row_ma_ct_data = params.row.ma_ct_data) === null || _params_row_ma_ct_data === void 0 ? void 0 : _params_row_ma_ct_data.ma_ct) || \"\",\n                    // onValueChange={(newValue: any) => {\n                    //   onCellValueChange(params.row.uuid, 'ma_ct', newValue);\n                    // }}\n                    searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].CHUNG_TU),\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.chungTuSearchColumns,\n                    dialogTitle: \"Danh mục chứng từ\",\n                    className: \"w-full\",\n                    columnDisplay: \"ma_ct\",\n                    onRowSelection: (selectedChungTu)=>{\n                        if (selectedChungTu) {\n                            console.log(\"selectedChungTu\", params);\n                            onCellValueChange(params.row.uuid, \"ma_ct_data\", selectedChungTu.ma_ct || \"\");\n                            // onCellValueChange(params.row.uuid, 'ten_ct', selectedChungTu.ten_ct || '');\n                            if (onChungTuSelect) {\n                                onChungTuSelect(params.row.uuid, selectedChungTu);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"ten_ct\",\n            headerName: \"T\\xean chứng từ\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ten_ct\",\n                    type: \"text\",\n                    value: params.row.ten_ct,\n                    // onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ten_ct', newValue)}\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, undefined)\n        },\n        {\n            field: \"username\",\n            headerName: \"Người sử dụng Nh\\xf3m\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.username || \"\",\n                    onValueChange: (newValue)=>{\n                        onCellValueChange(params.row.uuid, \"username\", newValue);\n                        // Khi người dùng xóa giá trị, cũng xóa related field\n                        if (!newValue) {\n                            onCellValueChange(params.row.uuid, \"first_name\", \"\");\n                        }\n                    },\n                    otherSearchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].NGUOI_SU_DUNG),\n                    searchEndpoint: \"\",\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.userSearchColumns,\n                    dialogTitle: \"Danh mục người sử dụng\",\n                    className: \"w-full\",\n                    displayRelatedField: \"first_name\",\n                    columnDisplay: \"username\",\n                    relatedFieldValue: params.row.first_name || \"\",\n                    onRowSelection: (selectedUser)=>{\n                        if (selectedUser) {\n                            onCellValueChange(params.row.uuid, \"username\", selectedUser.username);\n                            onCellValueChange(params.row.uuid, \"first_name\", selectedUser.first_name);\n                            // Call parent callback for additional processing\n                            if (onUserSelect) {\n                                onUserSelect(params.row.uuid, selectedUser);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"first_name\",\n            headerName: \"T\\xean người sử dụng\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"first_name\",\n                    type: \"text\",\n                    value: params.row.first_name,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"first_name\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\n"));

/***/ })

});